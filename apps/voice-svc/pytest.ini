[tool:pytest]
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*
addopts = 
    -v
    --tb=short
    --strict-markers
    --disable-warnings
    --cov=avr
    --cov=services
    --cov=routers
    --cov-report=term-missing
    --cov-report=html
    --cov-fail-under=90
markers =
    asyncio: marks tests as async
    unit: marks tests as unit tests
    integration: marks tests as integration tests
asyncio_mode = auto
