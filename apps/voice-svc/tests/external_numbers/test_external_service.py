"""
Tests for ExternalNumberService.

Tests cover:
- External number creation and validation
- E.164 format validation
- Premium number blocking
- Verification code generation and validation
- Status management
- Error handling
"""
import pytest
from unittest.mock import AsyncMock, patch
from uuid import uuid4
from datetime import datetime, timezone

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.exc import IntegrityError

from packages.shared.models import ExternalNumber, ExternalNumberStatus
from services.external_number_service import (
    ExternalNumberService,
    DuplicateNumberError,
    NumberNotFoundError,
    InvalidVerificationCodeError,
    ExternalNumberError
)


class TestExternalNumberService:
    """Test suite for ExternalNumberService."""
    
    @pytest.fixture
    def mock_db(self):
        """Mock database session."""
        return AsyncMock(spec=AsyncSession)
    
    @pytest.fixture
    def service(self, mock_db):
        """ExternalNumberService instance with mocked dependencies."""
        return ExternalNumberService(mock_db)
    
    @pytest.fixture
    def tenant_id(self):
        """Sample tenant ID."""
        return uuid4()
    
    @pytest.fixture
    def valid_did(self):
        """Valid phone number in E.164 format."""
        return "+15558675309"
    
    @pytest.fixture
    def sample_external_number(self, tenant_id, valid_did):
        """Sample ExternalNumber instance."""
        return ExternalNumber(
            id=uuid4(),
            tenant_id=tenant_id,
            did=valid_did,
            status=ExternalNumberStatus.PENDING,
            verification_code="123456",
            created_at=datetime.now(timezone.utc)
        )

    class TestCreateExternalNumber:
        """Tests for create_external_number method."""
        
        async def test_create_external_number_success(self, service, mock_db, tenant_id, valid_did):
            """Test successful external number creation."""
            # Setup
            mock_db.commit = AsyncMock()
            mock_db.refresh = AsyncMock()
            
            # Execute
            result = await service.create_external_number(tenant_id, valid_did)
            
            # Verify
            assert result.tenant_id == tenant_id
            assert result.did == valid_did
            assert result.status == ExternalNumberStatus.PENDING
            assert len(result.verification_code) == 6
            assert result.verification_code.isdigit()
            
            mock_db.add.assert_called_once()
            mock_db.commit.assert_called_once()
            mock_db.refresh.assert_called_once()
        
        async def test_create_external_number_duplicate(self, service, mock_db, tenant_id, valid_did):
            """Test creation with duplicate number."""
            # Setup
            mock_db.commit = AsyncMock(side_effect=IntegrityError("", "", "unique constraint"))
            mock_db.rollback = AsyncMock()
            
            # Execute & Verify
            with pytest.raises(DuplicateNumberError):
                await service.create_external_number(tenant_id, valid_did)
            
            mock_db.rollback.assert_called_once()
        
        @pytest.mark.parametrize("invalid_did,expected_error", [
            ("", "Phone number is required"),
            ("1234567890", "Phone number must be in E.164 format"),
            ("+1900555123", "Premium numbers are not allowed"),
            ("+1976555123", "Premium numbers are not allowed"),
            ("+123", "Phone number must be in E.164 format"),
        ])
        async def test_create_external_number_invalid_did(
            self, service, tenant_id, invalid_did, expected_error
        ):
            """Test creation with invalid DIDs."""
            with pytest.raises(ExternalNumberError, match=expected_error):
                await service.create_external_number(tenant_id, invalid_did)

    class TestMarkNumberVerifying:
        """Tests for mark_number_verifying method."""
        
        async def test_mark_number_verifying_success(self, service, mock_db, valid_did):
            """Test successful status update to verifying."""
            # Setup
            mock_result = AsyncMock()
            mock_result.rowcount = 1
            mock_db.execute = AsyncMock(return_value=mock_result)
            mock_db.commit = AsyncMock()
            
            # Execute
            result = await service.mark_number_verifying(valid_did)
            
            # Verify
            assert result is True
            mock_db.execute.assert_called_once()
            mock_db.commit.assert_called_once()
        
        async def test_mark_number_verifying_not_found(self, service, mock_db, valid_did):
            """Test marking non-existent number as verifying."""
            # Setup
            mock_result = AsyncMock()
            mock_result.rowcount = 0
            mock_db.execute = AsyncMock(return_value=mock_result)
            mock_db.commit = AsyncMock()
            
            # Execute
            result = await service.mark_number_verifying(valid_did)
            
            # Verify
            assert result is False

    class TestVerifyCode:
        """Tests for verify_code method."""
        
        async def test_verify_code_success(self, service, mock_db, valid_did, sample_external_number):
            """Test successful code verification."""
            # Setup
            mock_result = AsyncMock()
            mock_result.scalar_one_or_none = AsyncMock(return_value=sample_external_number)
            mock_db.execute = AsyncMock(return_value=mock_result)
            mock_db.commit = AsyncMock()
            
            # Execute
            result = await service.verify_code(valid_did, "123456")
            
            # Verify
            assert result is True
            assert mock_db.execute.call_count == 2  # select + update
            mock_db.commit.assert_called_once()
        
        async def test_verify_code_number_not_found(self, service, mock_db, valid_did):
            """Test verification with non-existent number."""
            # Setup
            mock_result = AsyncMock()
            mock_result.scalar_one_or_none = AsyncMock(return_value=None)
            mock_db.execute = AsyncMock(return_value=mock_result)
            
            # Execute & Verify
            with pytest.raises(NumberNotFoundError):
                await service.verify_code(valid_did, "123456")
        
        async def test_verify_code_invalid_code(self, service, mock_db, valid_did, sample_external_number):
            """Test verification with wrong code."""
            # Setup
            mock_result = AsyncMock()
            mock_result.scalar_one_or_none = AsyncMock(return_value=sample_external_number)
            mock_db.execute = AsyncMock(return_value=mock_result)
            
            # Execute & Verify
            with pytest.raises(InvalidVerificationCodeError):
                await service.verify_code(valid_did, "wrong_code")

    class TestUtilityMethods:
        """Tests for utility methods."""
        
        def test_generate_sip_uri(self, service, valid_did):
            """Test SIP URI generation."""
            result = service.generate_sip_uri(valid_did)
            expected = f"sip:{valid_did}@voice-svc:5060"
            assert result == expected
        
        def test_generate_sip_uri_custom_domain(self, mock_db):
            """Test SIP URI generation with custom domain."""
            with patch.dict('os.environ', {'VOICE_SVC_DOMAIN': 'custom.domain', 'VOICE_SVC_PORT': '5061'}):
                service = ExternalNumberService(mock_db)
                result = service.generate_sip_uri("+15558675309")
                expected = "sip:+<EMAIL>:5061"
                assert result == expected
        
        @pytest.mark.parametrize("did,expected", [
            ("+15558675309", "+15558675309"),
            ("+1 555 867 5309", "+15558675309"),
            ("+1-555-867-5309", "+15558675309"),
            ("+1 (555) 867-5309", "+15558675309"),
        ])
        def test_validate_e164_formatting(self, service, did, expected):
            """Test E.164 validation with various formats."""
            result = service._validate_e164(did)
            assert result == expected
        
        def test_generate_verification_code(self, service):
            """Test verification code generation."""
            code = service._generate_verification_code()
            assert len(code) == 6
            assert code.isdigit()
            assert 100000 <= int(code) <= 999999
