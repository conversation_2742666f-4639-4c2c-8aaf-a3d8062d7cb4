"""
Tests for CallVerificationService.

Tests cover:
- Incoming call handling for external numbers
- Verification flow detection
- Code verification during calls
- IVR prompt generation
- Status management during verification
"""
import pytest
from unittest.mock import AsyncMock
from uuid import uuid4
from datetime import datetime, timezone

from sqlalchemy.ext.asyncio import AsyncSession

from packages.shared.models import ExternalNumber, ExternalNumberStatus
from services.call_verification_service import CallVerificationService
from services.external_number_service import NumberNotFoundError


class TestCallVerificationService:
    """Test suite for CallVerificationService."""
    
    @pytest.fixture
    def mock_db(self):
        """Mock database session."""
        return AsyncMock(spec=AsyncSession)
    
    @pytest.fixture
    def service(self, mock_db):
        """CallVerificationService instance with mocked dependencies."""
        return CallVerificationService(mock_db)
    
    @pytest.fixture
    def tenant_id(self):
        """Sample tenant ID."""
        return uuid4()
    
    @pytest.fixture
    def valid_did(self):
        """Valid phone number in E.164 format."""
        return "+15558675309"
    
    @pytest.fixture
    def caller_number(self):
        """Caller's phone number."""
        return "+15551234567"
    
    @pytest.fixture
    def pending_external_number(self, tenant_id, valid_did):
        """External number in pending status."""
        return ExternalNumber(
            id=uuid4(),
            tenant_id=tenant_id,
            did=valid_did,
            status=ExternalNumberStatus.PENDING,
            verification_code="123456",
            created_at=datetime.now(timezone.utc)
        )
    
    @pytest.fixture
    def verified_external_number(self, tenant_id, valid_did):
        """External number in verified status."""
        return ExternalNumber(
            id=uuid4(),
            tenant_id=tenant_id,
            did=valid_did,
            status=ExternalNumberStatus.VERIFIED,
            verification_code="123456",
            created_at=datetime.now(timezone.utc),
            verified_at=datetime.now(timezone.utc)
        )

    class TestHandleIncomingCall:
        """Tests for handle_incoming_call method."""
        
        async def test_handle_incoming_call_normal_number(self, service, caller_number):
            """Test handling call to non-external number."""
            # Setup - mock external service to return None
            service.external_service.get_external_number_by_did = AsyncMock(return_value=None)
            
            # Execute
            result = await service.handle_incoming_call(caller_number, "+15559876543")
            
            # Verify
            assert result["requires_verification"] is False
            assert result["status"] == "normal_call"
            assert "normal call flow" in result["message"]
        
        async def test_handle_incoming_call_verified_number(
            self, service, caller_number, valid_did, verified_external_number
        ):
            """Test handling call to verified external number."""
            # Setup
            service.external_service.get_external_number_by_did = AsyncMock(
                return_value=verified_external_number
            )
            
            # Execute
            result = await service.handle_incoming_call(caller_number, valid_did)
            
            # Verify
            assert result["requires_verification"] is False
            assert result["status"] == "verified"
            assert "normal call flow" in result["message"]
        
        async def test_handle_incoming_call_pending_number(
            self, service, caller_number, valid_did, pending_external_number
        ):
            """Test handling call to pending external number."""
            # Setup
            service.external_service.get_external_number_by_did = AsyncMock(
                return_value=pending_external_number
            )
            service.external_service.mark_number_verifying = AsyncMock(return_value=True)
            
            # Execute
            result = await service.handle_incoming_call(caller_number, valid_did)
            
            # Verify
            assert result["requires_verification"] is True
            assert result["verification_code"] == "123456"
            assert result["status"] == "verification_required"
            assert "enter your 6-digit verification code" in result["message"]
            assert "prompt" in result
            
            # Verify service was called to mark as verifying
            service.external_service.mark_number_verifying.assert_called_once_with(valid_did)
        
        async def test_handle_incoming_call_verifying_number(
            self, service, caller_number, valid_did, pending_external_number
        ):
            """Test handling call to number already in verifying status."""
            # Setup
            pending_external_number.status = ExternalNumberStatus.VERIFYING
            service.external_service.get_external_number_by_did = AsyncMock(
                return_value=pending_external_number
            )
            
            # Execute
            result = await service.handle_incoming_call(caller_number, valid_did)
            
            # Verify
            assert result["requires_verification"] is True
            assert result["verification_code"] == "123456"
            assert result["status"] == "verification_in_progress"
            assert "enter your 6-digit verification code" in result["message"]
        
        async def test_handle_incoming_call_failed_number(
            self, service, caller_number, valid_did, pending_external_number
        ):
            """Test handling call to failed external number."""
            # Setup
            pending_external_number.status = ExternalNumberStatus.FAILED
            service.external_service.get_external_number_by_did = AsyncMock(
                return_value=pending_external_number
            )
            
            # Execute
            result = await service.handle_incoming_call(caller_number, valid_did)
            
            # Verify
            assert result["requires_verification"] is False
            assert result["status"] == "verification_failed"
            assert "failed verification" in result["message"]
            assert result.get("error") is True

    class TestProcessVerificationInput:
        """Tests for process_verification_input method."""
        
        async def test_process_verification_input_success(self, service, valid_did):
            """Test successful verification code processing."""
            # Setup
            service.external_service.verify_code = AsyncMock(return_value=True)
            
            # Execute
            result = await service.process_verification_input(valid_did, "123456")
            
            # Verify
            assert result["verified"] is True
            assert result["status"] == "verified"
            assert "verified" in result["message"]
            assert result["continue_call"] is True
            
            service.external_service.verify_code.assert_called_once_with(valid_did, "123456")
        
        async def test_process_verification_input_invalid_code(self, service, valid_did):
            """Test verification with invalid code."""
            # Setup
            service.external_service.verify_code = AsyncMock(return_value=False)
            
            # Execute
            result = await service.process_verification_input(valid_did, "wrong")
            
            # Verify
            assert result["verified"] is False
            assert result["status"] == "verification_failed"
            assert "Invalid code" in result["message"]
            assert result["continue_call"] is False
        
        async def test_process_verification_input_number_not_found(self, service, valid_did):
            """Test verification for non-existent number."""
            # Setup
            service.external_service.verify_code = AsyncMock(
                side_effect=NumberNotFoundError("Not found")
            )
            
            # Execute
            result = await service.process_verification_input(valid_did, "123456")
            
            # Verify
            assert result["verified"] is False
            assert result["status"] == "number_not_found"
            assert "not found" in result["message"].lower()
            assert result["continue_call"] is False

    class TestPromptGeneration:
        """Tests for prompt generation methods."""
        
        def test_get_verification_prompt(self, service):
            """Test verification prompt generation."""
            prompt = service._get_verification_prompt()
            
            assert "AI Voice Receptionist" in prompt
            assert "6-digit verification code" in prompt
            assert "pound key" in prompt
        
        def test_get_verification_success_message(self, service):
            """Test success message generation."""
            message = service.get_verification_success_message()
            
            assert "successfully verified" in message
            assert "AI Voice Receptionist" in message
            assert "transfer" in message
        
        def test_get_verification_failure_message(self, service):
            """Test failure message generation."""
            message = service.get_verification_failure_message()
            
            assert "not correct" in message
            assert "try calling again" in message
            assert "support" in message

    class TestGetVerificationStatus:
        """Tests for get_verification_status method."""
        
        async def test_get_verification_status_found(
            self, service, valid_did, pending_external_number
        ):
            """Test getting status for existing external number."""
            # Setup
            service.external_service.get_external_number_by_did = AsyncMock(
                return_value=pending_external_number
            )
            
            # Execute
            result = await service.get_verification_status(valid_did)
            
            # Verify
            assert result is not None
            assert result["did"] == valid_did
            assert result["status"] == "pending"
            assert result["requires_verification"] is True
            assert "created_at" in result
        
        async def test_get_verification_status_not_found(self, service, valid_did):
            """Test getting status for non-existent number."""
            # Setup
            service.external_service.get_external_number_by_did = AsyncMock(return_value=None)
            
            # Execute
            result = await service.get_verification_status(valid_did)
            
            # Verify
            assert result is None
        
        async def test_get_verification_status_verified_number(
            self, service, valid_did, verified_external_number
        ):
            """Test getting status for verified number."""
            # Setup
            service.external_service.get_external_number_by_did = AsyncMock(
                return_value=verified_external_number
            )
            
            # Execute
            result = await service.get_verification_status(valid_did)
            
            # Verify
            assert result is not None
            assert result["status"] == "verified"
            assert result["requires_verification"] is False
            assert result["verified_at"] is not None
