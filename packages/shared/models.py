import os
import enum
import uuid
from datetime import datetime

from sqlalchemy import (
    Column,
    String,
    DateTime,
    ForeignKey,
    Enum as SAEnum,
    JSON,
    CHAR,
    Text,
    Index,
    Boolean,
)
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy import String as _String
from sqlalchemy.orm import declarative_base, relationship

IS_TEST = os.environ.get("ENVIRONMENT") == "test"
SCHEMA = None if IS_TEST else "tenants"
UuidType = UUID(as_uuid=True) if not IS_TEST else _String

Base = declarative_base()


class BookingProvider(str, enum.Enum):
    GOOGLE = "google"
    CALENDLY = "calendly"


class CallbackStatus(str, enum.Enum):
    OPEN = "open"
    DONE = "done"


class DeliveryChannel(str, enum.Enum):
    EMAIL = "email"
    SMS = "sms"


class DeliveryProvider(str, enum.Enum):
    RESEND = "resend"
    TELNYX = "telnyx"


class DeliveryStatus(str, enum.Enum):
    DELIVERED = "delivered"
    FAILED = "failed"


class TelnyxNumberStatus(str, enum.Enum):
    PENDING = "pending"
    ACTIVE = "active"
    INACTIVE = "inactive"
    FAILED = "failed"


class TelnyxConnectionStatus(str, enum.Enum):
    PROVISIONED = "provisioned"
    FORWARDING = "forwarding"
    SIP = "sip"


class ExternalNumberStatus(str, enum.Enum):
    PENDING = "pending"
    VERIFYING = "verifying"
    VERIFIED = "verified"
    FAILED = "failed"


class Call(Base):
    __tablename__ = "calls"
    __table_args__ = {"schema": SCHEMA}

    id = Column(UuidType, primary_key=True, default=uuid.uuid4)
    firm_id = Column(
        UuidType,
        ForeignKey("tenants.firms.id" if not IS_TEST else "firms.id"),
        nullable=False,
        index=True,
    )
    started_at = Column(DateTime(timezone=True), nullable=False)
    ended_at = Column(DateTime(timezone=True))
    from_number = Column(String)
    to_number = Column(String)
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(
        DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False
    )

    intake_responses = relationship("IntakeResponse", back_populates="call")


class IntakeResponse(Base):
    __tablename__ = "intake_responses"
    __table_args__ = {"schema": SCHEMA}

    id = Column(UuidType, primary_key=True, default=uuid.uuid4)
    call_id = Column(
        UuidType,
        ForeignKey(("tenants." if not IS_TEST else "") + "calls.id"),
        nullable=False,
        index=True,
    )
    question = Column(String, nullable=False)
    answer = Column(String)
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)

    call = relationship("Call", back_populates="intake_responses")


class Booking(Base):
    __tablename__ = "bookings"
    __table_args__ = (
        Index("idx_bookings_firm_start", "firm_id", "start_at"),
        {"schema": SCHEMA},
    )

    id = Column(UuidType, primary_key=True, default=uuid.uuid4)
    firm_id = Column(
        UuidType,
        ForeignKey("tenants.firms.id" if not IS_TEST else "firms.id"),
        nullable=False,
        index=True,
    )
    provider = Column(
        SAEnum(BookingProvider, name="booking_provider_enum", create_type=False),
        nullable=False,
    )
    external_id = Column(String)
    start_at = Column(DateTime(timezone=True))
    booked_at = Column(DateTime(timezone=True))
    metadata_ = Column("metadata", JSON, default=dict)
    provider_event_link = Column(Text)
    recep_webhook_status = Column(Text, server_default="pending")
    caller_name = Column(String)
    caller_email = Column(String)
    calendar_event_id = Column(String)
    end_ts = Column(DateTime(timezone=True))
    status = Column(String)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)


class CallbackTask(Base):
    __tablename__ = "callback_tasks"
    __table_args__ = {"schema": SCHEMA}

    id = Column(UuidType, primary_key=True, default=uuid.uuid4)
    firm_id = Column(
        UuidType,
        ForeignKey("tenants.firms.id" if not IS_TEST else "firms.id"),
        nullable=False,
        index=True,
    )
    call_id = Column(
        UuidType,
        ForeignKey(("tenants." if not IS_TEST else "") + "calls.id"),
        nullable=True,
        index=True,
    )
    status = Column(
        SAEnum(CallbackStatus, name="callback_status_enum"),
        nullable=False,
        default=CallbackStatus.OPEN,
    )
    note = Column(String)
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    completed_at = Column(DateTime)


class StateMeta(Base):
    __tablename__ = "state_meta"
    __table_args__ = {"schema": SCHEMA}

    firm_id = Column(
        UuidType,
        ForeignKey("tenants.firms.id" if not IS_TEST else "firms.id"),
        primary_key=True,
    )
    state_code = Column(CHAR(2), nullable=False)


class DeliveryReceipt(Base):
    __tablename__ = "delivery_receipts"
    __table_args__ = (
        Index("idx_delivery_receipts_tenant_status", "tenant_id", "status"),
        Index("idx_delivery_receipts_message_id", "message_id"),
        {"schema": SCHEMA},
    )

    id = Column(UuidType, primary_key=True, default=uuid.uuid4)
    tenant_id = Column(UuidType, nullable=False, index=True)
    message_id = Column(Text, nullable=False)
    channel = Column(
        SAEnum(DeliveryChannel, name="delivery_channel_enum", create_type=False),
        nullable=False,
    )
    provider = Column(
        SAEnum(DeliveryProvider, name="delivery_provider_enum", create_type=False),
        nullable=False,
    )
    status = Column(
        SAEnum(DeliveryStatus, name="delivery_status_enum", create_type=False),
        nullable=False,
    )
    delivered_at = Column(DateTime(timezone=True), nullable=True)
    failure_reason = Column(Text, nullable=True)
    raw_payload = Column(JSON, default=dict)
    created_at = Column(DateTime(timezone=True), default=datetime.utcnow, nullable=False)


class TelnyxNumber(Base):
    __tablename__ = "telnyx_numbers"
    __table_args__ = (
        Index("idx_telnyx_numbers_tenant_status", "tenant_id", "status"),
        Index("idx_telnyx_numbers_state_status", "state", "status"),
        Index("idx_telnyx_numbers_connection_status", "connection_status"),
        Index("idx_telnyx_numbers_verification_required", "verification_required"),
        {"schema": SCHEMA},
    )

    id = Column(UuidType, primary_key=True, default=uuid.uuid4)
    tenant_id = Column(UuidType, nullable=False, index=True)
    did = Column(String(20), nullable=False, unique=True, index=True)
    state = Column(String(2), nullable=False, index=True)
    forwarding_number = Column(String(20), nullable=True)
    status = Column(
        SAEnum(TelnyxNumberStatus, name="telnyx_number_status_enum", create_type=False),
        nullable=False,
        default=TelnyxNumberStatus.PENDING,
        index=True,
    )
    connection_status = Column(
        SAEnum(TelnyxConnectionStatus, name="telnyx_connection_status_enum", create_type=False),
        nullable=False,
        default=TelnyxConnectionStatus.SIP,
        index=True,
    )
    verification_required = Column(Boolean, nullable=False, default=False, index=True)
    telnyx_number_id = Column(String(100), nullable=True)  # Telnyx's internal ID
    telnyx_connection_id = Column(String(100), nullable=True)  # Telnyx connection ID
    metadata_ = Column("metadata", JSON, default=dict)  # Additional Telnyx data
    created_at = Column(DateTime(timezone=True), default=datetime.utcnow, nullable=False)
    updated_at = Column(
        DateTime(timezone=True),
        default=datetime.utcnow,
        onupdate=datetime.utcnow,
        nullable=False
    )


class ExternalNumber(Base):
    __tablename__ = "external_numbers"
    __table_args__ = (
        Index("idx_external_numbers_tenant_status", "tenant_id", "status"),
        Index("idx_external_numbers_status", "status"),
        {"schema": SCHEMA},
    )

    id = Column(UuidType, primary_key=True, default=uuid.uuid4)
    tenant_id = Column(UuidType, nullable=False, index=True)
    did = Column(String(20), nullable=False, unique=True, index=True)
    status = Column(
        SAEnum(ExternalNumberStatus, name="external_number_status_enum", create_type=False),
        nullable=False,
        default=ExternalNumberStatus.PENDING,
        index=True,
    )
    verification_code = Column(CHAR(6), nullable=False)
    created_at = Column(DateTime(timezone=True), default=datetime.utcnow, nullable=False)
    verified_at = Column(DateTime(timezone=True), nullable=True)
